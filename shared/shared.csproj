<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>VidCompressor</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CasCap.Api.GooglePhotos" Version="3.1.0" />
    <PackageReference Include="Google.Apis.Auth" Version="1.70.0" />
    <PackageReference Include="Google.Apis.PhotosLibrary.v1" Version="1.34.0.1223" />
    <PackageReference Include="Google.Cloud.Firestore" Version="3.10.0" />
    <PackageReference Include="Google.Cloud.SecretManager.V1" Version="2.5.0" />
    <PackageReference Include="Google.Cloud.Storage.V1" Version="4.10.0" />
    <PackageReference Include="Google.Cloud.Video.Transcoder.V1" Version="2.6.0" />
    <PackageReference Include="Google.Cloud.Tasks.V2" Version="3.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.7" />
    <PackageReference Include="Polly" Version="8.6.2" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.11" />
    <PackageReference Include="MetadataExtractor" Version="2.8.1" />
    <PackageReference Include="FFMpegCore" Version="5.2.0" />
    <PackageReference Include="Stripe.net" Version="48.3.0" />
  </ItemGroup>

</Project>
